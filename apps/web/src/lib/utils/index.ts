/**
 * BuddyChip Utility Library Index
 * 
 * Centralized export of all utility functions for consistent imports
 * across the application. This provides a single source of truth for
 * all utility functions and ensures consistent usage patterns.
 */

// Core utilities
export * from "./date";
export * from "./errors";
export * from "./api";
export * from "./validation";
export * from "./performance";
export * from "./features";

// Re-export commonly used utilities with shorter names
export { dateUtils as date } from "./date";
export { errorUtils as errors } from "./errors";
export { apiUtils as api } from "./api";
export { validationUtils as validation } from "./validation";
export { performanceUtils as performance } from "./performance";
export { featureUtils as features } from "./features";

// Legacy compatibility - maintain existing cn function
export { cn } from "../utils";

/**
 * Utility library metadata
 */
export const UTILS_VERSION = "1.0.0";
export const UTILS_LOADED_AT = new Date().toISOString();

/**
 * Quick access to most commonly used utilities
 */
export const utils = {
  // Date utilities
  formatDate: (date: Date, format?: "short" | "long" | "relative" | "time") => 
    require("./date").dateUtils.formatDate(date, format),
  
  getTimeAgo: (date: Date) => 
    require("./date").dateUtils.getTimeAgo(date),
  
  getCurrentBillingPeriod: () => 
    require("./date").dateUtils.getCurrentBillingPeriod(),
  
  // Error utilities
  createTRPCError: (code: string, message: string, cause?: Error) => 
    require("./errors").errorUtils.createTRPCError(code, message, cause),
  
  handleDatabaseError: (error: unknown, operation: string) => 
    require("./errors").errorUtils.handleDatabaseError(error, operation),
  
  getSafeErrorMessage: (error: unknown) => 
    require("./errors").errorUtils.getSafeErrorMessage(error),
  
  // API utilities
  createSuccessResponse: <T>(data: T, message?: string) => 
    require("./api").apiUtils.createSuccessResponse(data, message),
  
  createErrorResponse: (error: string, code?: string) => 
    require("./api").apiUtils.createErrorResponse(error, code),
  
  withRetry: <T>(operation: () => Promise<T>, options?: any) => 
    require("./api").apiUtils.withRetry(operation, options),
  
  // Validation utilities
  sanitizeText: (text: string) => 
    require("./validation").validationUtils.sanitizeText(text),
  
  validateTwitterHandle: (handle: string) => 
    require("./validation").validationUtils.validateTwitterHandle(handle),
  
  validateURL: (url: string) => 
    require("./validation").validationUtils.validateURL(url),
  
  // Performance utilities
  debounce: <T extends (...args: any[]) => any>(func: T, delay: number) => 
    require("./performance").performanceUtils.debounce(func, delay),
  
  throttle: <T extends (...args: any[]) => any>(func: T, limit: number) => 
    require("./performance").performanceUtils.throttle(func, limit),
  
  createCache: <T>(options?: any) => 
    require("./performance").performanceUtils.createCache(options),
  
  // Feature utilities
  getFeatureName: (feature: string) => 
    require("./features").featureUtils.getFeatureName(feature),
  
  formatUsage: (used: number, limit: number, feature: string) => 
    require("./features").featureUtils.formatUsage(used, limit, feature),
  
  isLimitExceeded: (used: number, limit: number) => 
    require("./features").featureUtils.isLimitExceeded(used, limit),
} as const;

/**
 * Type-safe utility function registry
 */
export interface UtilityRegistry {
  date: typeof import("./date").dateUtils;
  errors: typeof import("./errors").errorUtils;
  api: typeof import("./api").apiUtils;
  validation: typeof import("./validation").validationUtils;
  performance: typeof import("./performance").performanceUtils;
  features: typeof import("./features").featureUtils;
}

/**
 * Development utilities for debugging and testing
 */
export const devUtils = {
  /**
   * Log utility usage for debugging
   */
  logUtilityUsage: (utilityName: string, functionName: string, args?: any[]) => {
    if (process.env.NODE_ENV === "development") {
      console.log(`🔧 Utility used: ${utilityName}.${functionName}`, args);
    }
  },
  
  /**
   * Validate utility function exists
   */
  validateUtility: (utilityName: keyof UtilityRegistry, functionName: string): boolean => {
    try {
      const utility = require(`./${utilityName}`);
      const utilityObject = utility[`${utilityName}Utils`];
      return typeof utilityObject[functionName] === "function";
    } catch {
      return false;
    }
  },
  
  /**
   * Get all available utilities
   */
  getAvailableUtilities: (): Record<string, string[]> => {
    const utilities: Record<string, string[]> = {};
    
    try {
      const dateUtils = require("./date").dateUtils;
      utilities.date = Object.keys(dateUtils);
      
      const errorUtils = require("./errors").errorUtils;
      utilities.errors = Object.keys(errorUtils);
      
      const apiUtils = require("./api").apiUtils;
      utilities.api = Object.keys(apiUtils);
      
      const validationUtils = require("./validation").validationUtils;
      utilities.validation = Object.keys(validationUtils);
      
      const performanceUtils = require("./performance").performanceUtils;
      utilities.performance = Object.keys(performanceUtils);
      
      const featureUtils = require("./features").featureUtils;
      utilities.features = Object.keys(featureUtils);
    } catch (error) {
      console.error("Error loading utilities:", error);
    }
    
    return utilities;
  },
  
  /**
   * Performance benchmark for utilities
   */
  benchmark: async (utilityName: string, functionName: string, iterations: number = 1000) => {
    const start = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      // This would call the actual utility function
      // Implementation depends on specific utility
    }
    
    const end = performance.now();
    const avgTime = (end - start) / iterations;
    
    console.log(`📊 Benchmark: ${utilityName}.${functionName} - ${avgTime.toFixed(4)}ms avg`);
    
    return {
      totalTime: end - start,
      averageTime: avgTime,
      iterations,
    };
  },
} as const;

/**
 * Utility health check
 */
export const healthCheck = {
  /**
   * Check if all utilities are loaded correctly
   */
  checkUtilities: (): { status: "healthy" | "degraded" | "unhealthy"; details: Record<string, boolean> } => {
    const checks: Record<string, boolean> = {};
    
    try {
      // Check each utility module
      checks.date = typeof require("./date").dateUtils === "object";
      checks.errors = typeof require("./errors").errorUtils === "object";
      checks.api = typeof require("./api").apiUtils === "object";
      checks.validation = typeof require("./validation").validationUtils === "object";
      checks.performance = typeof require("./performance").performanceUtils === "object";
      checks.features = typeof require("./features").featureUtils === "object";
      
      const healthyCount = Object.values(checks).filter(Boolean).length;
      const totalCount = Object.keys(checks).length;
      
      let status: "healthy" | "degraded" | "unhealthy";
      if (healthyCount === totalCount) {
        status = "healthy";
      } else if (healthyCount > totalCount / 2) {
        status = "degraded";
      } else {
        status = "unhealthy";
      }
      
      return { status, details: checks };
    } catch (error) {
      console.error("Utility health check failed:", error);
      return { status: "unhealthy", details: checks };
    }
  },
  
  /**
   * Get utility library statistics
   */
  getStats: () => {
    const utilities = devUtils.getAvailableUtilities();
    const totalFunctions = Object.values(utilities).reduce((sum, funcs) => sum + funcs.length, 0);
    
    return {
      version: UTILS_VERSION,
      loadedAt: UTILS_LOADED_AT,
      modules: Object.keys(utilities).length,
      totalFunctions,
      moduleBreakdown: Object.fromEntries(
        Object.entries(utilities).map(([name, funcs]) => [name, funcs.length])
      ),
    };
  },
} as const;

// Initialize health check on load
if (process.env.NODE_ENV === "development") {
  const health = healthCheck.checkUtilities();
  console.log(`🏥 Utility library health: ${health.status}`, health.details);
  
  const stats = healthCheck.getStats();
  console.log(`📊 Utility library stats:`, stats);
}

console.log("🚀 BuddyChip utility library loaded successfully");
