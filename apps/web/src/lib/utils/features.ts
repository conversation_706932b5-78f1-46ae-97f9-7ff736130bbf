/**
 * Feature Utilities for BuddyChip
 * 
 * Comprehensive utilities for feature management, subscription plans,
 * usage tracking, and feature flag handling.
 */

import { FeatureType, type SubscriptionPlan } from "../../../prisma/generated/index.js";

/**
 * Feature metadata for display and configuration
 */
export const FEATURE_METADATA = {
  [FeatureType.AI_CALLS]: {
    name: "AI Calls",
    icon: "Sparkles",
    unit: "calls",
    description: "AI-powered response generation",
    category: "ai",
    priority: 1,
  },
  [FeatureType.IMAGE_GENERATIONS]: {
    name: "Image Generations",
    icon: "Image",
    unit: "images",
    description: "AI-generated images for tweets",
    category: "ai",
    priority: 2,
  },
  [FeatureType.MONITORED_ACCOUNTS]: {
    name: "Monitored Accounts",
    icon: "User",
    unit: "accounts",
    description: "Twitter accounts to monitor",
    category: "monitoring",
    priority: 3,
  },
  [FeatureType.PERSONA_GENERATIONS]: {
    name: "Persona Generations",
    icon: "Users",
    unit: "personas",
    description: "AI-generated personas from Twitter data per month",
    category: "ai",
    priority: 4,
  },
  [FeatureType.PERSONA_MEMORY_OPS]: {
    name: "Persona Memory",
    icon: "Brain",
    unit: "operations",
    description: "Memory operations for persona data storage",
    category: "ai",
    priority: 5,
  },
  [FeatureType.COOKIE_API_CALLS]: {
    name: "Cookie API Calls",
    icon: "Code",
    unit: "calls",
    description: "Cookie.fun API calls for crypto data",
    category: "integration",
    priority: 6,
  },
  [FeatureType.MENTIONS_PER_MONTH]: {
    name: "Mentions Per Month",
    icon: "Calendar",
    unit: "mentions",
    description: "Monthly mention processing limit",
    category: "monitoring",
    priority: 7,
  },
  [FeatureType.MENTIONS_PER_SYNC]: {
    name: "Mentions Per Sync",
    icon: "Refresh",
    unit: "mentions",
    description: "Mentions fetched per sync operation",
    category: "monitoring",
    priority: 8,
  },
  [FeatureType.MAX_TOTAL_MENTIONS]: {
    name: "Max Total Mentions",
    icon: "Database",
    unit: "mentions",
    description: "Maximum total unreplied mentions per account",
    category: "monitoring",
    priority: 9,
  },
  [FeatureType.STORAGE_GB]: {
    name: "Storage",
    icon: "HardDrive",
    unit: "GB",
    description: "Data storage allocation",
    category: "storage",
    priority: 10,
  },
  [FeatureType.TEAM_MEMBERS]: {
    name: "Team Members",
    icon: "Users",
    unit: "members",
    description: "Maximum team members",
    category: "team",
    priority: 11,
  },
} as const;

/**
 * Subscription plan metadata
 */
export const PLAN_METADATA = {
  free: {
    name: "Free",
    icon: "Zap",
    color: "gray",
    badge: null,
    description: "Get started with basic features",
    price: 0,
    billing: "monthly",
    popular: false,
  },
  "reply-guy": {
    name: "Reply Guy",
    icon: "Zap",
    color: "blue",
    badge: "Popular",
    description: "Perfect for active social media users",
    price: 29,
    billing: "monthly",
    popular: true,
  },
  "reply-god": {
    name: "Reply God",
    icon: "Crown",
    color: "purple",
    badge: "Recommended",
    description: "Advanced features for power users",
    price: 99,
    billing: "monthly",
    popular: false,
  },
  team: {
    name: "Team",
    icon: "Users",
    color: "orange",
    badge: "Enterprise",
    description: "Collaboration features for teams",
    price: 299,
    billing: "monthly",
    popular: false,
  },
} as const;

/**
 * Default feature limits by plan
 */
export const PLAN_LIMITS = {
  free: {
    [FeatureType.AI_CALLS]: 10,
    [FeatureType.IMAGE_GENERATIONS]: 2,
    [FeatureType.MONITORED_ACCOUNTS]: 1,
    [FeatureType.PERSONA_GENERATIONS]: 0,
    [FeatureType.PERSONA_MEMORY_OPS]: 0,
    [FeatureType.COOKIE_API_CALLS]: 0,
    [FeatureType.MENTIONS_PER_MONTH]: 100,
    [FeatureType.MENTIONS_PER_SYNC]: 10,
    [FeatureType.MAX_TOTAL_MENTIONS]: 50,
    [FeatureType.STORAGE_GB]: 1,
    [FeatureType.TEAM_MEMBERS]: 1,
  },
  "reply-guy": {
    [FeatureType.AI_CALLS]: 500,
    [FeatureType.IMAGE_GENERATIONS]: 50,
    [FeatureType.MONITORED_ACCOUNTS]: 5,
    [FeatureType.PERSONA_GENERATIONS]: 10,
    [FeatureType.PERSONA_MEMORY_OPS]: 100,
    [FeatureType.COOKIE_API_CALLS]: 50,
    [FeatureType.MENTIONS_PER_MONTH]: 1000,
    [FeatureType.MENTIONS_PER_SYNC]: 50,
    [FeatureType.MAX_TOTAL_MENTIONS]: 500,
    [FeatureType.STORAGE_GB]: 10,
    [FeatureType.TEAM_MEMBERS]: 3,
  },
  "reply-god": {
    [FeatureType.AI_CALLS]: 2000,
    [FeatureType.IMAGE_GENERATIONS]: 200,
    [FeatureType.MONITORED_ACCOUNTS]: 20,
    [FeatureType.PERSONA_GENERATIONS]: 100,
    [FeatureType.PERSONA_MEMORY_OPS]: 1000,
    [FeatureType.COOKIE_API_CALLS]: 1000,
    [FeatureType.MENTIONS_PER_MONTH]: 5000,
    [FeatureType.MENTIONS_PER_SYNC]: 100,
    [FeatureType.MAX_TOTAL_MENTIONS]: 2000,
    [FeatureType.STORAGE_GB]: 50,
    [FeatureType.TEAM_MEMBERS]: 10,
  },
  team: {
    [FeatureType.AI_CALLS]: 10000,
    [FeatureType.IMAGE_GENERATIONS]: 1000,
    [FeatureType.MONITORED_ACCOUNTS]: 100,
    [FeatureType.PERSONA_GENERATIONS]: 1000,
    [FeatureType.PERSONA_MEMORY_OPS]: 10000,
    [FeatureType.COOKIE_API_CALLS]: 10000,
    [FeatureType.MENTIONS_PER_MONTH]: 50000,
    [FeatureType.MENTIONS_PER_SYNC]: 500,
    [FeatureType.MAX_TOTAL_MENTIONS]: 20000,
    [FeatureType.STORAGE_GB]: 1000,
    [FeatureType.TEAM_MEMBERS]: 100,
  },
} as const;

/**
 * Feature categories for organization
 */
export const FEATURE_CATEGORIES = {
  ai: {
    name: "AI Features",
    description: "Artificial intelligence powered capabilities",
    icon: "Brain",
    color: "purple",
  },
  monitoring: {
    name: "Monitoring",
    description: "Account and content monitoring features",
    icon: "Eye",
    color: "blue",
  },
  analytics: {
    name: "Analytics",
    description: "Data analysis and reporting features",
    icon: "BarChart",
    color: "green",
  },
  customization: {
    name: "Customization",
    description: "Personalization and customization options",
    icon: "Settings",
    color: "orange",
  },
  integration: {
    name: "Integration",
    description: "API and third-party integrations",
    icon: "Link",
    color: "gray",
  },
} as const;

/**
 * Core feature utilities
 */
export const featureUtils = {
  /**
   * Get feature display name
   */
  getFeatureName: (feature: FeatureType): string => {
    return FEATURE_METADATA[feature]?.name || feature;
  },
  
  /**
   * Get feature metadata
   */
  getFeatureMetadata: (feature: FeatureType) => {
    return FEATURE_METADATA[feature] || {
      name: feature,
      icon: "Circle",
      unit: "units",
      description: "",
      category: "other",
      priority: 999,
    };
  },
  
  /**
   * Get plan metadata
   */
  getPlanMetadata: (plan: SubscriptionPlan) => {
    return PLAN_METADATA[plan.name as keyof typeof PLAN_METADATA] || {
      name: plan.name,
      icon: "Circle",
      color: "gray",
      badge: null,
      description: "",
      price: 0,
      billing: "monthly",
      popular: false,
    };
  },
  
  /**
   * Get feature limit for a plan
   */
  getFeatureLimit: (plan: SubscriptionPlan, feature: FeatureType): number => {
    return PLAN_LIMITS[plan.name as keyof typeof PLAN_LIMITS]?.[feature] || 0;
  },
  
  /**
   * Check if feature is available for plan
   */
  isFeatureAvailable: (plan: SubscriptionPlan, feature: FeatureType): boolean => {
    return featureUtils.getFeatureLimit(plan, feature) > 0;
  },
  
  /**
   * Get all available features for a plan
   */
  getAvailableFeatures: (plan: SubscriptionPlan): FeatureType[] => {
    return Object.entries(PLAN_LIMITS[plan.name as keyof typeof PLAN_LIMITS] || {})
      .filter(([_, limit]) => (limit as number) > 0)
      .map(([feature, _]) => feature as FeatureType);
  },
  
  /**
   * Format feature usage display
   */
  formatUsage: (used: number, limit: number, feature: FeatureType): string => {
    const { unit } = featureUtils.getFeatureMetadata(feature);
    
    if (limit === 0) {
      return "Not available";
    }
    
    if (limit === -1) {
      return `${used} ${unit} (unlimited)`;
    }
    
    return `${used} / ${limit} ${unit}`;
  },
  
  /**
   * Calculate usage percentage
   */
  getUsagePercentage: (used: number, limit: number): number => {
    if (limit === 0 || limit === -1) return 0;
    return Math.min(100, Math.round((used / limit) * 100));
  },
  
  /**
   * Check if feature limit is exceeded
   */
  isLimitExceeded: (used: number, limit: number): boolean => {
    if (limit === -1) return false; // Unlimited
    return used >= limit;
  },
  
  /**
   * Get usage status with color coding
   */
  getUsageStatus: (used: number, limit: number): {
    status: "safe" | "warning" | "danger" | "exceeded";
    color: string;
    message: string;
  } => {
    if (limit === -1) {
      return {
        status: "safe",
        color: "green",
        message: "Unlimited usage",
      };
    }
    
    if (limit === 0) {
      return {
        status: "exceeded",
        color: "red",
        message: "Feature not available",
      };
    }
    
    const percentage = featureUtils.getUsagePercentage(used, limit);
    
    if (used >= limit) {
      return {
        status: "exceeded",
        color: "red",
        message: "Limit exceeded",
      };
    }
    
    if (percentage >= 90) {
      return {
        status: "danger",
        color: "red",
        message: "Near limit",
      };
    }
    
    if (percentage >= 75) {
      return {
        status: "warning",
        color: "orange",
        message: "Approaching limit",
      };
    }
    
    return {
      status: "safe",
      color: "green",
      message: "Within limit",
    };
  },
  
  /**
   * Get features by category
   */
  getFeaturesByCategory: (category: string): FeatureType[] => {
    return Object.entries(FEATURE_METADATA)
      .filter(([_, metadata]) => metadata.category === category)
      .map(([feature, _]) => feature as FeatureType)
      .sort((a, b) => {
        const priorityA = FEATURE_METADATA[a].priority;
        const priorityB = FEATURE_METADATA[b].priority;
        return priorityA - priorityB;
      });
  },
  
  /**
   * Compare plans and highlight differences
   */
  comparePlans: (planA: SubscriptionPlan, planB: SubscriptionPlan) => {
    const limitsA = PLAN_LIMITS[planA.name as keyof typeof PLAN_LIMITS] || {};
    const limitsB = PLAN_LIMITS[planB.name as keyof typeof PLAN_LIMITS] || {};
    
    const allFeatures = new Set([
      ...Object.keys(limitsA),
      ...Object.keys(limitsB),
    ]) as Set<FeatureType>;
    
    const comparison: Array<{
      feature: FeatureType;
      planA: number;
      planB: number;
      difference: "better" | "worse" | "same";
      improvement?: number;
    }> = [];
    
    for (const feature of allFeatures) {
      const limitA = limitsA[feature] || 0;
      const limitB = limitsB[feature] || 0;
      
      let difference: "better" | "worse" | "same" = "same";
      let improvement: number | undefined;
      
      if (limitB > limitA) {
        difference = "better";
        improvement = limitA > 0 ? Math.round((limitB / limitA) * 100) - 100 : Infinity;
      } else if (limitB < limitA) {
        difference = "worse";
        improvement = limitB > 0 ? Math.round((limitA / limitB) * 100) - 100 : Infinity;
      }
      
      comparison.push({
        feature,
        planA: limitA,
        planB: limitB,
        difference,
        improvement,
      });
    }
    
    return comparison.sort((a, b) => {
      const priorityA = FEATURE_METADATA[a.feature].priority;
      const priorityB = FEATURE_METADATA[b.feature].priority;
      return priorityA - priorityB;
    });
  },
  
  /**
   * Get recommended plan based on usage patterns
   */
  getRecommendedPlan: (usage: Partial<Record<FeatureType, number>>): {
    plan: SubscriptionPlan;
    reason: string;
    savings?: number;
  } => {
    const planNames = Object.keys(PLAN_LIMITS) as Array<keyof typeof PLAN_LIMITS>;
    
    // Find the cheapest plan that meets all usage requirements
    for (const planName of planNames) {
      const limits = PLAN_LIMITS[planName];
      const meetsRequirements = Object.entries(usage).every(([feature, used]) => {
        const limit = limits[feature as FeatureType] || 0;
        return used <= limit;
      });
      
      if (meetsRequirements) {
        const metadata = PLAN_METADATA[planName];
        return {
          plan: planName as unknown as SubscriptionPlan,
          reason: `Meets all your usage requirements at $${metadata.price}/month`,
        };
      }
    }
    
    // If no plan meets requirements, recommend the highest tier
    return {
      plan: "team" as unknown as SubscriptionPlan,
      reason: "Your usage exceeds standard plans - team plan recommended",
    };
  },
  
  /**
   * Calculate cost savings for annual billing
   */
  calculateAnnualSavings: (plan: SubscriptionPlan, discount: number = 0.2): {
    monthly: number;
    annual: number;
    savings: number;
    savingsPercentage: number;
  } => {
    const metadata = PLAN_METADATA[plan.name as keyof typeof PLAN_METADATA];
    const monthly = metadata.price;
    const annual = Math.round(monthly * 12 * (1 - discount));
    const savings = (monthly * 12) - annual;
    const savingsPercentage = Math.round(discount * 100);
    
    return {
      monthly,
      annual,
      savings,
      savingsPercentage,
    };
  },
} as const;

/**
 * Feature flag utilities
 */
export const featureFlagUtils = {
  /**
   * Check if feature flag is enabled
   */
  isEnabled: (flag: string, userId?: string, defaultValue: boolean = false): boolean => {
    // In a real implementation, this would check against a feature flag service
    // For now, we'll use environment variables
    const envValue = process.env[`FEATURE_${flag.toUpperCase()}`];
    
    if (envValue !== undefined) {
      return envValue === "true" || envValue === "1";
    }
    
    return defaultValue;
  },
  
  /**
   * Get feature flag value with type safety
   */
  getValue: <T>(flag: string, defaultValue: T, userId?: string): T => {
    const envValue = process.env[`FEATURE_${flag.toUpperCase()}`];
    
    if (envValue === undefined) {
      return defaultValue;
    }
    
    // Try to parse as JSON for complex types
    try {
      return JSON.parse(envValue);
    } catch {
      // Return as string if not valid JSON
      return envValue as T;
    }
  },
  
  /**
   * Check multiple feature flags at once
   */
  checkFlags: (flags: string[], userId?: string): Record<string, boolean> => {
    const result: Record<string, boolean> = {};
    
    for (const flag of flags) {
      result[flag] = featureFlagUtils.isEnabled(flag, userId);
    }
    
    return result;
  },
} as const;

console.log("🎯 Feature utilities loaded successfully");
