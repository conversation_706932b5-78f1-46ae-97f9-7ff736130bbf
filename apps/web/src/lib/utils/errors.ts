/**
 * Error Handling Utilities for BuddyChip
 * 
 * Comprehensive error handling, logging, and recovery utilities
 * for consistent error management across the application.
 */

import { TRPCError } from "@trpc/server";
import { ZodError } from "zod";

/**
 * Custom application error class with context
 */
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public isOperational: boolean = true,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = "AppError";
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

/**
 * Error categories for better organization
 */
export enum ErrorCategory {
  VALIDATION = "validation",
  AUTHENTICATION = "authentication",
  AUTHORIZATION = "authorization",
  DATABASE = "database",
  EXTERNAL_API = "external_api",
  RATE_LIMIT = "rate_limit",
  NETWORK = "network",
  SYSTEM = "system",
}

/**
 * Comprehensive error utilities
 */
export const errorUtils = {
  /**
   * Create standardized TRPC error with context
   */
  createTRPCError: (
    code: "BAD_REQUEST" | "UNAUTHORIZED" | "FORBIDDEN" | "NOT_FOUND" | "CONFLICT" | "TOO_MANY_REQUESTS" | "INTERNAL_SERVER_ERROR",
    message: string,
    cause?: Error,
    context?: Record<string, any>
  ) => {
    console.error(`🚨 TRPC Error [${code}]:`, { message, context, cause });
    
    return new TRPCError({ 
      code, 
      message, 
      cause,
      ...(context && { cause: { ...cause, context } })
    });
  },
  
  /**
   * Handle database errors with proper context and logging
   */
  handleDatabaseError: (error: unknown, operation: string, context?: Record<string, any>): never => {
    console.error(`💾 Database error in ${operation}:`, { error, context });
    
    if (error && typeof error === "object" && "code" in error) {
      // Prisma error codes
      switch (error.code) {
        case "P2002":
          throw errorUtils.createTRPCError(
            "CONFLICT",
            "This resource already exists",
            error as unknown as Error,
            { operation, ...context }
          );
        case "P2025":
          throw errorUtils.createTRPCError(
            "NOT_FOUND",
            "The requested resource was not found",
            error as unknown as Error,
            { operation, ...context }
          );
        case "P2003":
          throw errorUtils.createTRPCError(
            "BAD_REQUEST",
            "Foreign key constraint failed",
            error as unknown as Error,
            { operation, ...context }
          );
        case "P2034":
          throw errorUtils.createTRPCError(
            "CONFLICT",
            "Transaction failed due to write conflict",
            error as unknown as Error,
            { operation, ...context }
          );
      }
    }
    
    throw errorUtils.createTRPCError(
      "INTERNAL_SERVER_ERROR",
      `Database operation failed: ${operation}`,
      error as Error,
      context
    );
  },
  
  /**
   * Format validation errors for user display
   */
  formatValidationErrors: (error: ZodError): Record<string, string> => {
    const formatted: Record<string, string> = {};
    
    error.errors.forEach(err => {
      const path = err.path.join(".");
      formatted[path] = err.message;
    });
    
    return formatted;
  },
  
  /**
   * Handle validation errors consistently
   */
  handleValidationError: (error: ZodError, context?: string): never => {
    const formattedErrors = errorUtils.formatValidationErrors(error);
    const message = `Validation failed${context ? ` for ${context}` : ""}`;
    
    console.warn(`⚠️ Validation error:`, { message, errors: formattedErrors });
    
    throw errorUtils.createTRPCError(
      "BAD_REQUEST",
      message,
      error,
      { validationErrors: formattedErrors }
    );
  },
  
  /**
   * Check if error is retryable
   */
  isRetryableError: (error: unknown): boolean => {
    if (error instanceof TRPCError) {
      return ["INTERNAL_SERVER_ERROR", "TOO_MANY_REQUESTS"].includes(error.code);
    }
    
    if (error instanceof Error) {
      const retryablePatterns = [
        "timeout",
        "ECONNREFUSED",
        "ETIMEDOUT",
        "ENOTFOUND",
        "socket hang up",
        "network error",
        "connection reset",
      ];
      
      return retryablePatterns.some(pattern => 
        error.message.toLowerCase().includes(pattern.toLowerCase())
      );
    }
    
    return false;
  },
  
  /**
   * Get error severity based on error type
   */
  getErrorSeverity: (error: unknown): ErrorSeverity => {
    if (error instanceof TRPCError) {
      switch (error.code) {
        case "INTERNAL_SERVER_ERROR":
          return ErrorSeverity.CRITICAL;
        case "UNAUTHORIZED":
        case "FORBIDDEN":
          return ErrorSeverity.HIGH;
        case "NOT_FOUND":
        case "CONFLICT":
          return ErrorSeverity.MEDIUM;
        default:
          return ErrorSeverity.LOW;
      }
    }
    
    if (error instanceof AppError) {
      return error.statusCode >= 500 ? ErrorSeverity.CRITICAL : ErrorSeverity.MEDIUM;
    }
    
    return ErrorSeverity.MEDIUM;
  },
  
  /**
   * Categorize error for better organization
   */
  categorizeError: (error: unknown): ErrorCategory => {
    if (error instanceof ZodError) {
      return ErrorCategory.VALIDATION;
    }
    
    if (error instanceof TRPCError) {
      switch (error.code) {
        case "UNAUTHORIZED":
          return ErrorCategory.AUTHENTICATION;
        case "FORBIDDEN":
          return ErrorCategory.AUTHORIZATION;
        case "TOO_MANY_REQUESTS":
          return ErrorCategory.RATE_LIMIT;
        case "BAD_REQUEST":
          return ErrorCategory.VALIDATION;
        default:
          return ErrorCategory.SYSTEM;
      }
    }
    
    if (error instanceof Error) {
      if (error.message.includes("database") || error.message.includes("prisma")) {
        return ErrorCategory.DATABASE;
      }
      
      if (error.message.includes("fetch") || error.message.includes("network")) {
        return ErrorCategory.NETWORK;
      }
      
      if (error.message.includes("API") || error.message.includes("external")) {
        return ErrorCategory.EXTERNAL_API;
      }
    }
    
    return ErrorCategory.SYSTEM;
  },
  
  /**
   * Log error with structured data
   */
  logError: (
    error: unknown,
    context: {
      operation: string;
      userId?: string;
      metadata?: Record<string, any>;
    }
  ): void => {
    const severity = errorUtils.getErrorSeverity(error);
    const category = errorUtils.categorizeError(error);
    
    const logData = {
      timestamp: new Date().toISOString(),
      severity,
      category,
      operation: context.operation,
      userId: context.userId,
      error: {
        name: error instanceof Error ? error.name : "Unknown",
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      },
      metadata: context.metadata,
    };
    
    // Use appropriate log level based on severity
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        console.error("🔥 CRITICAL ERROR:", logData);
        break;
      case ErrorSeverity.HIGH:
        console.error("🚨 HIGH SEVERITY ERROR:", logData);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn("⚠️ MEDIUM SEVERITY ERROR:", logData);
        break;
      case ErrorSeverity.LOW:
        console.info("ℹ️ LOW SEVERITY ERROR:", logData);
        break;
    }
  },
  
  /**
   * Safe error message extraction for user display
   */
  getSafeErrorMessage: (error: unknown): string => {
    if (error instanceof TRPCError) {
      return error.message;
    }
    
    if (error instanceof AppError) {
      return error.message;
    }
    
    if (error instanceof ZodError) {
      return "Invalid input data provided";
    }
    
    if (error instanceof Error) {
      // Don't expose internal error details to users
      if (error.message.includes("database") || error.message.includes("internal")) {
        return "An internal error occurred. Please try again.";
      }
      return error.message;
    }
    
    return "An unexpected error occurred";
  },
} as const;

/**
 * Error boundary utilities for React components
 */
export const errorBoundaryUtils = {
  /**
   * Handle component errors gracefully
   */
  handleComponentError: (error: Error, errorInfo: { componentStack: string }): void => {
    errorUtils.logError(error, {
      operation: "component_render",
      metadata: {
        componentStack: errorInfo.componentStack,
      },
    });
  },
  
  /**
   * Get fallback UI message based on error
   */
  getFallbackMessage: (error: Error): string => {
    const category = errorUtils.categorizeError(error);
    
    switch (category) {
      case ErrorCategory.NETWORK:
        return "Network connection issue. Please check your internet connection.";
      case ErrorCategory.AUTHENTICATION:
        return "Authentication required. Please sign in again.";
      case ErrorCategory.AUTHORIZATION:
        return "You don't have permission to access this resource.";
      default:
        return "Something went wrong. Please refresh the page and try again.";
    }
  },
} as const;

console.log("🚨 Error utilities loaded successfully");
